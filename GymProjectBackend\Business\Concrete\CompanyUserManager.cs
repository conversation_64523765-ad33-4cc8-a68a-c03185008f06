﻿using Business.Abstract;
using Business.BusinessAscpects.Autofac;
using Business.Constants;
using Business.ValidationRules.FluentValidation;
using Core.Aspects.Autofac.Caching;
using Core.Aspects.Autofac.Logging;
using Core.Aspects.Autofac.Performance;
using Core.Aspects.Autofac.Validation;
using Core.Aspects.Autofac.Transaction;
using Core.Entities.Concrete;
using Core.Utilities.Results;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;

namespace Business.Concrete
{
    public class CompanyUserManager : ICompanyUserService
    {
        ICompanyUserDal _companyUserDal;
        IUserService _userService;
        ICompanyService _companyService;
        ICompanyAdressService _companyAdressService;
        IMemberService _memberService;
        IUserCompanyService _userCompanyService;
        IMembershipService _membershipService;
        IMembershipTypeService _membershipTypeService;
        IPaymentService _paymentService;
        IExpenseService _expenseService;
        ICompanyExerciseService _companyExerciseService;
        IProductService _productService;

        public CompanyUserManager(ICompanyUserDal companyUserDal, IUserService userService, ICompanyService companyService,
            ICompanyAdressService companyAdressService, IMemberService memberService, IUserCompanyService userCompanyService,
            IMembershipService membershipService, IMembershipTypeService membershipTypeService, IPaymentService paymentService,
            IExpenseService expenseService, ICompanyExerciseService companyExerciseService, IProductService productService)
        {
            _companyUserDal = companyUserDal;
            _userService = userService;
            _companyService = companyService;
            _companyAdressService = companyAdressService;
            _memberService = memberService;
            _userCompanyService = userCompanyService;
            _membershipService = membershipService;
            _membershipTypeService = membershipTypeService;
            _paymentService = paymentService;
            _expenseService = expenseService;
            _companyExerciseService = companyExerciseService;
            _productService = productService;
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("CompanyUser")]
        [ValidationAspect(typeof(CompanyUserValidator))]
        public IResult Add(CompanyUser companyUser)
        {
            _companyUserDal.Add(companyUser);
            return new SuccessResult(Messages.UserAdded);
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [SmartCacheRemoveAspect("CompanyUser")]
        public IResult Delete(int id)
        {
            _companyUserDal.Delete(id);
            return new SuccessResult(Messages.UserDeleted);
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 480, "CompanyUser", "Master")]
        public IDataResult<List<CompanyUser>> GetAll()
        {
            return new SuccessDataResult<List<CompanyUser>>(_companyUserDal.GetAll(), Messages.CompanyUserGetAll);
        }
        [SecuredOperation("owner")]
        [MultiTenantCacheAspect(duration: 480, "CompanyUser", "City")]
        public IDataResult<List<CompanyUser>> GetByCityId(int cityId)
        {
            return new SuccessDataResult<List<CompanyUser>>(_companyUserDal.GetAll(c => c.CityID == cityId));

        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 480, "CompanyUser", "Details")]
        public IDataResult<List<CompanyUserDetailDto>> GetCompanyUserDetails()
        {
            return new SuccessDataResult<List<CompanyUserDetailDto>>(_companyUserDal.GetCompanyUserDetails());
        }
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 480, "CompanyUser", "CompanyDetails")]
        public IDataResult<List<CompanyDetailDto>> GetCompanyDetails()
        {
            return new SuccessDataResult<List<CompanyDetailDto>>(_companyUserDal.GetCompanyDetails());
        }
        [SecuredOperation("owner")]
        [MultiTenantCacheAspect(duration: 480, "CompanyUser", "CityDetails")]
        public IDataResult<List<CompanyDetailDto>> GetCompanyUserDetailsByCityId(int cityId)
        {
            return new SuccessDataResult<List<CompanyDetailDto>>(_companyUserDal.GetCompanyUserDetailsByCityId(cityId));
        }
        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [SmartCacheRemoveAspect("CompanyUser")]
        [ValidationAspect(typeof(CompanyUserValidator))]
        public IResult Update(CompanyUser companyUser)
        {
            _companyUserDal.Update(companyUser);
            return new SuccessResult(Messages.UserUpdated);
        }

        // Yeni eklenen metodlar
        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "CompanyUser", "ById")]
        public IDataResult<CompanyUser> GetById(int companyUserID)
        {
            var companyUser = _companyUserDal.Get(cu => cu.CompanyUserID == companyUserID);
            if (companyUser == null)
            {
                return new ErrorDataResult<CompanyUser>("Şirket kullanıcısı bulunamadı");
            }
            return new SuccessDataResult<CompanyUser>(companyUser);
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "CompanyUser", "FullDetails")]
        public IDataResult<CompanyUserFullDetailDto> GetCompanyUserFullDetails(int companyUserID)
        {
            var result = _companyUserDal.GetCompanyUserFullDetails(companyUserID);
            if (result != null)
            {
                return new SuccessDataResult<CompanyUserFullDetailDto>(result);
            }
            return new ErrorDataResult<CompanyUserFullDetailDto>("Kullanıcı detayları bulunamadı");
        }

        [SecuredOperation("owner")]
        [PerformanceAspect(3)]
        [MultiTenantCacheAspect(duration: 30, "CompanyUser", "Paginated")]
        public IDataResult<PaginatedCompanyUserDto> GetCompanyUsersPaginated(int pageNumber, int pageSize, string searchTerm = "")
        {
            var result = _companyUserDal.GetCompanyUsersPaginated(pageNumber, pageSize, searchTerm);
            if (result != null)
            {
                return new SuccessDataResult<PaginatedCompanyUserDto>(result);
            }
            return new ErrorDataResult<PaginatedCompanyUserDto>("Sayfalanmış veriler alınamadı");
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [TransactionScopeAspect] // Çoklu tablo güncellemesi için transaction
        [SmartCacheRemoveAspect("CompanyUser")]
        [ValidationAspect(typeof(CompanyUserValidator))]
        public IResult UpdateCompanyUserFull(CompanyUserFullUpdateDto updateDto)
        {
            try
            {
                // 1. Mevcut CompanyUser kaydını al
                var existingCompanyUser = _companyUserDal.Get(cu => cu.CompanyUserID == updateDto.CompanyUserID);
                if (existingCompanyUser == null)
                {
                    return new ErrorResult("Şirket kullanıcısı bulunamadı");
                }

                string oldEmail = existingCompanyUser.Email;
                string oldName = existingCompanyUser.Name;

                // 2. CompanyUser tablosunu güncelle
                existingCompanyUser.Name = updateDto.Name;
                existingCompanyUser.PhoneNumber = updateDto.PhoneNumber;
                existingCompanyUser.Email = updateDto.Email;
                existingCompanyUser.CityID = updateDto.CityID;
                existingCompanyUser.TownID = updateDto.TownID;
                existingCompanyUser.IsActive = updateDto.IsActive;
                existingCompanyUser.UpdatedDate = DateTime.Now;

                _companyUserDal.Update(existingCompanyUser);

                // 3. Email veya isim değişmişse User tablosunu güncelle
                bool emailChanged = oldEmail != updateDto.Email;
                bool nameChanged = oldName != updateDto.Name;

                if (emailChanged || nameChanged)
                {
                    var existingUser = _userService.GetByMail(oldEmail);
                    if (existingUser != null)
                    {
                        // Email güncelle
                        if (emailChanged)
                        {
                            existingUser.Email = updateDto.Email;
                        }

                        // İsim güncelle
                        if (nameChanged)
                        {
                            var (firstName, lastName) = ParseFullName(updateDto.Name);
                            existingUser.FirstName = firstName;
                            existingUser.LastName = lastName;
                        }

                        existingUser.UpdatedDate = DateTime.Now;
                        _userService.Update(existingUser);
                    }
                }

                // 4. Company bilgileri güncellenmişse Company tablosunu güncelle
                if (updateDto.CompanyDataChanged && !string.IsNullOrEmpty(updateDto.CompanyName))
                {
                    // CompanyUser'dan Company'yi bul (UserCompanies tablosu üzerinden)
                    using (var context = new GymContext())
                    {
                        var userCompany = context.UserCompanies
                            .FirstOrDefault(uc => uc.UserID == updateDto.CompanyUserID && uc.IsActive == true);

                        if (userCompany != null)
                        {
                            // Company tablosunu güncelle
                            var existingCompany = _companyService.GetById(userCompany.CompanyId);
                            if (existingCompany.Success && existingCompany.Data != null)
                            {
                                var company = existingCompany.Data;
                                company.CompanyName = updateDto.CompanyName;
                                company.PhoneNumber = updateDto.CompanyPhone;
                                company.UpdatedDate = DateTime.Now;

                                _companyService.Update(company);
                            }

                            // CompanyAddress tablosunu güncelle
                            var companyAddresses = _companyAdressService.GetAll();
                            if (companyAddresses.Success && companyAddresses.Data != null)
                            {
                                var existingAddress = companyAddresses.Data
                                    .FirstOrDefault(ca => ca.CompanyID == userCompany.CompanyId && ca.IsActive == true);

                                if (existingAddress != null)
                                {
                                    existingAddress.Adress = updateDto.CompanyAddress;
                                    existingAddress.CityID = updateDto.CompanyCityID ?? existingAddress.CityID;
                                    existingAddress.TownID = updateDto.CompanyTownID ?? existingAddress.TownID;
                                    existingAddress.UpdatedDate = DateTime.Now;

                                    _companyAdressService.Update(existingAddress);
                                }
                            }
                        }
                    }
                }

                return new SuccessResult("Şirket kullanıcısı ve şirket bilgileri başarıyla güncellendi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Güncelleme sırasında hata oluştu: {ex.Message}");
            }
        }

        /// <summary>
        /// İsmi FirstName ve LastName olarak ayırır
        /// Son kelimeyi LastName, geri kalanını FirstName olarak alır
        /// </summary>
        private (string FirstName, string LastName) ParseFullName(string fullName)
        {
            if (string.IsNullOrWhiteSpace(fullName))
            {
                return ("", "");
            }

            fullName = fullName.Trim();
            fullName = System.Text.RegularExpressions.Regex.Replace(fullName, @"\s+", " ");

            string[] nameParts = fullName.Split(' ', StringSplitOptions.RemoveEmptyEntries);

            if (nameParts.Length == 0)
            {
                return ("", "");
            }
            else if (nameParts.Length == 1)
            {
                return (nameParts[0], "");
            }
            else
            {
                string lastName = nameParts[nameParts.Length - 1];
                string firstName = string.Join(" ", nameParts.Take(nameParts.Length - 1));
                return (firstName, lastName);
            }
        }

        [SecuredOperation("owner")]
        [LogAspect]
        [PerformanceAspect(3)]
        [TransactionScopeAspect] // Çoklu tablo silme işlemi için transaction
        [SmartCacheRemoveAspect("CompanyUser")]
        public IResult DeleteCompanyUserFull(int companyUserID)
        {
            try
            {
                // 1. Mevcut CompanyUser kaydını al
                var existingCompanyUser = _companyUserDal.Get(cu => cu.CompanyUserID == companyUserID);
                if (existingCompanyUser == null)
                {
                    return new ErrorResult("Şirket kullanıcısı bulunamadı");
                }

                // 2. İlişkili UserCompany kaydını bul
                var userCompanies = _userCompanyService.GetAll();
                if (!userCompanies.Success)
                {
                    return new ErrorResult("UserCompany bilgileri alınamadı");
                }

                var userCompany = userCompanies.Data?.FirstOrDefault(uc => uc.UserID == companyUserID && uc.IsActive == true);
                if (userCompany == null)
                {
                    return new ErrorResult("İlişkili şirket bilgisi bulunamadı");
                }

                var companyId = userCompany.CompanyId;

                // 3. Salon ile ilişkili tüm verileri soft delete yap
                var deleteResult = DeleteAllCompanyRelatedData(companyId);
                if (!deleteResult.Success)
                {
                    return deleteResult;
                }

                // 4. Ana tabloları soft delete yap
                // CompanyUser
                existingCompanyUser.IsActive = false;
                existingCompanyUser.DeletedDate = DateTime.Now;
                existingCompanyUser.UpdatedDate = DateTime.Now;
                _companyUserDal.Update(existingCompanyUser);

                // UserCompany
                userCompany.IsActive = false;
                userCompany.DeletedDate = DateTime.Now;
                userCompany.UpdatedDate = DateTime.Now;
                _userCompanyService.Update(userCompany);

                // Company
                var companies = _companyService.GetAll();
                if (companies.Success && companies.Data != null)
                {
                    var company = companies.Data.FirstOrDefault(c => c.CompanyID == companyId && c.IsActive == true);
                    if (company != null)
                    {
                        company.IsActive = false;
                        company.DeletedDate = DateTime.Now;
                        company.UpdatedDate = DateTime.Now;
                        _companyService.Update(company);
                    }
                }

                // CompanyAddress
                var companyAddresses = _companyAdressService.GetAll();
                if (companyAddresses.Success && companyAddresses.Data != null)
                {
                    var address = companyAddresses.Data.FirstOrDefault(ca => ca.CompanyID == companyId && ca.IsActive == true);
                    if (address != null)
                    {
                        address.IsActive = false;
                        address.DeletedDate = DateTime.Now;
                        address.UpdatedDate = DateTime.Now;
                        _companyAdressService.Update(address);
                    }
                }

                return new SuccessResult("Salon ve tüm ilişkili veriler başarıyla silindi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"Silme işlemi sırasında hata oluştu: {ex.Message}");
            }
        }

        private IResult DeleteAllCompanyRelatedData(int companyId)
        {
            try
            {
                // Member tablosunu soft delete yap
                var members = _memberService.GetAll();
                if (members.Success && members.Data != null)
                {
                    var companyMembers = members.Data.Where(m => m.CompanyID == companyId && m.IsActive == true).ToList();
                    foreach (var member in companyMembers)
                    {
                        member.IsActive = false;
                        member.DeletedDate = DateTime.Now;
                        member.UpdatedDate = DateTime.Now;
                        _memberService.Update(member);
                    }
                }

                // Membership tablosunu soft delete yap
                var memberships = _membershipService.GetAll();
                if (memberships.Success && memberships.Data != null)
                {
                    var companyMemberships = memberships.Data.Where(m => m.CompanyID == companyId && m.IsActive == true).ToList();
                    foreach (var membership in companyMemberships)
                    {
                        membership.IsActive = false;
                        membership.DeletedDate = DateTime.Now;
                        membership.UpdatedDate = DateTime.Now;
                        _membershipService.Update(membership);
                    }
                }

                // MembershipType tablosunu soft delete yap
                var membershipTypes = _membershipTypeService.GetAll();
                if (membershipTypes.Success && membershipTypes.Data != null)
                {
                    var companyMembershipTypes = membershipTypes.Data.Where(mt => mt.CompanyID == companyId && mt.IsActive == true).ToList();
                    foreach (var membershipType in companyMembershipTypes)
                    {
                        membershipType.IsActive = false;
                        membershipType.DeletedDate = DateTime.Now;
                        membershipType.UpdatedDate = DateTime.Now;
                        _membershipTypeService.Update(membershipType);
                    }
                }

                // Payment tablosunu soft delete yap
                var payments = _paymentService.GetAll();
                if (payments.Success && payments.Data != null)
                {
                    var companyPayments = payments.Data.Where(p => p.CompanyID == companyId && p.IsActive == true).ToList();
                    foreach (var payment in companyPayments)
                    {
                        payment.IsActive = false;
                        payment.DeletedDate = DateTime.Now;
                        _paymentService.Update(payment);
                    }
                }

                // Expense tablosunu soft delete yap
                var expenses = _expenseService.GetAll();
                if (expenses.Success && expenses.Data != null)
                {
                    var companyExpenses = expenses.Data.Where(e => e.CompanyID == companyId && e.IsActive == true).ToList();
                    foreach (var expense in companyExpenses)
                    {
                        expense.IsActive = false;
                        expense.DeletedDate = DateTime.Now;
                        expense.UpdatedDate = DateTime.Now;
                        _expenseService.Update(expense);
                    }
                }

                // CompanyExercise tablosunu soft delete yap
                var companyExercises = _companyExerciseService.GetAll();
                if (companyExercises.Success && companyExercises.Data != null)
                {
                    var exercises = companyExercises.Data.Where(ce => ce.CompanyID == companyId && ce.IsActive == true).ToList();
                    foreach (var exercise in exercises)
                    {
                        exercise.IsActive = false;
                        exercise.DeletedDate = DateTime.Now;
                        exercise.UpdatedDate = DateTime.Now;
                        _companyExerciseService.Update(exercise);
                    }
                }

                // Product tablosunu soft delete yap
                var products = _productService.GetAll();
                if (products.Success && products.Data != null)
                {
                    var companyProducts = products.Data.Where(p => p.CompanyID == companyId && p.IsActive).ToList();
                    foreach (var product in companyProducts)
                    {
                        product.IsActive = false;
                        product.DeletedDate = DateTime.Now;
                        product.UpdatedDate = DateTime.Now;
                        _productService.Update(product);
                    }
                }

                return new SuccessResult("İlişkili veriler başarıyla silindi");
            }
            catch (Exception ex)
            {
                return new ErrorResult($"İlişkili verileri silerken hata oluştu: {ex.Message}");
            }
        }
    }
}
